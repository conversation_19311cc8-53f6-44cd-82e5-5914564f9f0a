gcc -MM *.c > depend
make ../bin/circle
make[1]: Entering directory '/mnt/c/Projects/Luminari-Source'
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o account.o account.c
account.c: In function ‘load_account’:
account.c:399:3: warning: ‘__builtin_strncpy’ specified bound 31 equals destination size [-Wstringop-truncation]
  399 |   strncpy(account->password, row[2], MAX_PWD_LENGTH + 1);
      |   ^
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o act.comm.o act.comm.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o act.comm.do_spec_comm.o act.comm.do_spec_comm.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o act.informative.o act.informative.c
act.informative.c: In function ‘impl_do_users_’:
act.informative.c:4665:9: warning: the comparison will always evaluate as ‘true’ for the address of ‘host’ will never be NULL [-Waddress]
 4665 |     if (d->host && *d->host)
      |         ^
In file included from act.informative.c:13:
structs.h:5556:10: note: ‘host’ declared here
 5556 |     char host[HOST_LENGTH + 1];        /**< hostname */
      |          ^~~~
act.informative.c: In function ‘impl_do_levels_’:
act.informative.c:4776:11: warning: the comparison will always evaluate as ‘true’ for the address of ‘arg’ will never be NULL [-Waddress]
 4776 |   if (arg != NULL && *arg)
      |           ^~
act.informative.c:4765:41: note: ‘arg’ declared here
 4765 |   char buf[MAX_STRING_LENGTH] = {'\0'}, arg[MAX_STRING_LENGTH] = {'\0'};
      |                                         ^~~
act.informative.c: In function ‘show_obj_to_char’:
act.informative.c:388:52: warning: ‘%s’ directive output may be truncated writing up to 99 bytes into a region of size between 89 and 98 [-Wformat-truncation=]
  388 |           snprintf(keyword1, sizeof(keyword1), "%d.%s", (item_num + 1), keyword);
      |                                                    ^~                   ~~~~~~~
In file included from /usr/include/stdio.h:980,
                 from sysdep.h:69,
                 from act.informative.c:12:
In function ‘snprintf’,
    inlined from ‘show_obj_to_char’ at act.informative.c:388:11:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 3 and 111 bytes into a destination of size 100
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
act.informative.c: In function ‘do_equipment’:
act.informative.c:3942:41: warning: ‘%d’ directive output may be truncated writing between 1 and 11 bytes into a region of size 10 [-Wformat-truncation=]
 3942 |     snprintf(dex_max, sizeof(dex_max), "%d", j);
      |                                         ^~
In function ‘impl_do_equipment_’,
    inlined from ‘do_equipment’ at act.informative.c:3928:1:
act.informative.c:3942:40: note: directive argument in the range [-2147483648, 98]
 3942 |     snprintf(dex_max, sizeof(dex_max), "%d", j);
      |                                        ^~~~
In function ‘snprintf’,
    inlined from ‘impl_do_equipment_’ at act.informative.c:3942:5,
    inlined from ‘do_equipment’ at act.informative.c:3928:1:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 2 and 12 bytes into a destination of size 10
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o act.item.o act.item.c
act.item.c: In function ‘name_from_drinkcon.part.0’:
act.item.c:2990:5: warning: ‘__builtin___strncat_chk’ specified bound depends on the length of the source argument [-Wstringop-overflow=]
 2990 |     strncat(new_name, cur_name, cpylen); /* strncat: OK (size precalculated) */
      |     ^
act.item.c:2983:16: note: length computed here
 2983 |       cpylen = strlen(cur_name);
      |                ^~~~~~~~~~~~~~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o act.movement.o act.movement.c
act.movement.c: In function ‘impl_do_transposition_’:
act.movement.c:4139:22: warning: initialization of ‘room_rnum’ {aka ‘unsigned int’} from ‘void *’ makes integer from pointer without a cast [-Wint-conversion]
 4139 |   room_rnum chRoom = NULL, eidolonRoom = NULL;
      |                      ^~~~
act.movement.c:4139:42: warning: initialization of ‘room_rnum’ {aka ‘unsigned int’} from ‘void *’ makes integer from pointer without a cast [-Wint-conversion]
 4139 |   room_rnum chRoom = NULL, eidolonRoom = NULL;
      |                                          ^~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o act.offensive.o act.offensive.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o act.other.o act.other.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o act.social.o act.social.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o act.wizard.o act.wizard.c
act.wizard.c: In function ‘impl_do_wizutil_’:
act.wizard.c:3161:26: warning: the comparison will always evaluate as ‘true’ for the address of ‘affected_by’ will never be NULL [-Waddress]
 3161 |       if (vict->affected || AFF_FLAGS(vict))
      |                          ^~
In file included from act.wizard.c:13:
structs.h:4664:9: note: ‘affected_by’ declared here
 4664 |     int affected_by[AF_ARRAY_MAX]; /**< Bitvector for spells/skills affected by */
      |         ^~~~~~~~~~~
act.wizard.c: In function ‘impl_do_recent_’:
act.wizard.c:7155:9: warning: the comparison will always evaluate as ‘true’ for the address of ‘host’ will never be NULL [-Waddress]
 7155 |     if (this->host && *(this->host))
      |         ^~~~
structs.h:5783:10: note: ‘host’ declared here
 5783 |     char host[HOST_LENGTH + 1]; /* Host IP address                 */
      |          ^~~~
act.wizard.c: In function ‘impl_do_objlist_.isra’:
act.wizard.c:7323:50: warning: ‘%s’ directive output may be truncated writing up to 8191 bytes into a region of size 1022 [-Wformat-truncation=]
 7323 |           snprintf(tmp_buf, sizeof(tmp_buf), "\tc%s\tn%s%d ", buf2, (obj->affected[m].modifier > 0 ? "+" : ""),
      |                                                  ^~           ~~~~
In file included from /usr/include/stdio.h:980,
                 from sysdep.h:69,
                 from act.wizard.c:12:
In function ‘snprintf’,
    inlined from ‘impl_do_objlist_.isra’ at act.wizard.c:7323:11:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 7 and 8209 bytes into a destination of size 1024
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
act.wizard.c: In function ‘impl_do_objlist_.isra’:
act.wizard.c:7341:61: warning: ‘%s’ directive output may be truncated writing up to 8191 bytes into a region of size 1008 [-Wformat-truncation=]
 7341 |       snprintf(tmp_buf, sizeof(tmp_buf), "      \tcWorn\tn: %s \tcAffects:\tn %s %s %s\r\n",
      |                                                             ^~
 7342 |                buf2, buf3, buf4, buf5);
      |                ~~~~                                          
In function ‘snprintf’,
    inlined from ‘impl_do_objlist_.isra’ at act.wizard.c:7341:7:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output 35 or more bytes (assuming 8226) into a destination of size 1024
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
act.wizard.c: In function ‘impl_do_last_.isra’:
act.wizard.c:2736:9: warning: ‘__builtin_strncpy’ output may be truncated copying 511 bytes from a string of length 511 [-Wstringop-truncation]
 2736 |         strncpy(name, arg, sizeof(name) - 1);
      |         ^
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o actionqueues.o actionqueues.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o actions.o actions.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o aedit.o aedit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o alchemy.o alchemy.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o asciimap.o asciimap.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o assign_wpn_armor.o assign_wpn_armor.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o backgrounds.o backgrounds.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o ban.o ban.c
ban.c: In function ‘valid_name’:
ban.c:281:20: warning: the comparison will always evaluate as ‘false’ for the address of ‘invalid_list’ will never be NULL [-Waddress]
  281 |   if (invalid_list == NULL || num_invalid < 1)
      |                    ^~
ban.c:29:14: note: ‘invalid_list’ declared here
   29 | static char *invalid_list[MAX_INVALID_NAMES];
      |              ^~~~~~~~~~~~
ban.c: In function ‘impl_do_ban_.isra’:
ban.c:188:3: warning: ‘__builtin_strncpy’ output may be truncated copying 50 bytes from a string of length 511 [-Wstringop-truncation]
  188 |   strncpy(ban_node->site, site, BANNED_SITE_LENGTH); /* strncpy: OK (b_n->site:BANNED_SITE_LENGTH+1) */
      |   ^
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o bardic_performance.o bardic_performance.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o boards.o boards.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o bsd-snprintf.o bsd-snprintf.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o cedit.o cedit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o char_descs.o char_descs.c
char_descs.c: In function ‘current_short_desc’:
char_descs.c:406:39: warning: ‘%s’ directive output may be truncated writing up to 127 bytes into a region of size between 1 and 256 [-Wformat-truncation=]
  406 |     snprintf(final, sizeof(final), "%s%s%s", desc, adj1, adj2);
      |                                       ^~           ~~~~
In file included from /usr/include/stdio.h:980,
                 from sysdep.h:69,
                 from char_descs.c:4:
In function ‘snprintf’,
    inlined from ‘current_short_desc’ at char_descs.c:406:5:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 1 and 510 bytes into a destination of size 256
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o clan.o clan.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o clan_edit.o clan_edit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o class.o class.c
class.c: In function ‘init_start_char’:
class.c:3101:20: warning: the comparison will always evaluate as ‘true’ for the address of ‘affected_by’ will never be NULL [-Waddress]
 3101 |   if (ch->affected || AFF_FLAGS(ch))
      |                    ^~
In file included from class.c:16:
structs.h:4664:9: note: ‘affected_by’ declared here
 4664 |     int affected_by[AF_ARRAY_MAX]; /**< Bitvector for spells/skills affected by */
      |         ^~~~~~~~~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o combat_modes.o combat_modes.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o comm.o comm.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o config.o config.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********   -DMKTIME=\""Fri Jul 25 01:22:04 IDT 2025"\" -DMKUSER=\""xamgibson"\" -DMKHOST=\"""\" -DBRANCH=\""* master"\" -DPARENT=\""85190524ed3310c985a9f14289df6121a88e710e"\"   -c -o constants.o constants.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o craft.o craft.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o crafting_new.o crafting_new.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o crafting_recipes.o crafting_recipes.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o crafts.o crafts.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o db.o db.c
db.c: In function ‘load_config’:
db.c:6358:18: warning: the comparison will always evaluate as ‘true’ for the address of ‘line’ will never be NULL [-Waddress]
 6358 |         if (line != NULL && *line)
      |                  ^~
db.c:6279:8: note: ‘line’ declared here
 6279 |   char line[MAX_STRING_LENGTH] = {'\0'};
      |        ^~~~
db.c:6367:18: warning: the comparison will always evaluate as ‘true’ for the address of ‘line’ will never be NULL [-Waddress]
 6367 |         if (line != NULL && *line)
      |                  ^~
db.c:6279:8: note: ‘line’ declared here
 6279 |   char line[MAX_STRING_LENGTH] = {'\0'};
      |        ^~~~
db.c:6435:18: warning: the comparison will always evaluate as ‘true’ for the address of ‘line’ will never be NULL [-Waddress]
 6435 |         if (line != NULL && *line)
      |                  ^~
db.c:6279:8: note: ‘line’ declared here
 6279 |   char line[MAX_STRING_LENGTH] = {'\0'};
      |        ^~~~
db.c: In function ‘parse_object’:
db.c:6025:3: warning: ‘__builtin_strncpy’ specified bound 512 equals destination size [-Wstringop-truncation]
 6025 |   strncpy(buf1, obj->short_description, sizeof(buf1));
      |   ^
db.c: In function ‘boot_db’:
db.c:1106:5: warning: ‘__builtin_strncpy’ specified bound 512 equals destination size [-Wstringop-truncation]
 1106 |     strncpy(buf1, zone_table[i].name, sizeof(buf1));
      |     ^
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o deities.o deities.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o desc_engine.o desc_engine.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o dg_comm.o dg_comm.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o dg_db_scripts.o dg_db_scripts.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o dg_event.o dg_event.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o dg_handler.o dg_handler.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o dg_misc.o dg_misc.c
dg_misc.c: In function ‘do_dg_affect’:
dg_misc.c:188:16: warning: the comparison will always evaluate as ‘false’ for the address of ‘charname’ will never be NULL [-Waddress]
  188 |   if (charname == NULL || !*charname || property == NULL || !*property ||
      |                ^~
dg_misc.c:177:8: note: ‘charname’ declared here
  177 |   char charname[MAX_INPUT_LENGTH] = {'\0'}, property[MAX_INPUT_LENGTH] = {'\0'};
      |        ^~~~~~~~
dg_misc.c:188:50: warning: the comparison will always evaluate as ‘false’ for the address of ‘property’ will never be NULL [-Waddress]
  188 |   if (charname == NULL || !*charname || property == NULL || !*property ||
      |                                                  ^~
dg_misc.c:177:45: note: ‘property’ declared here
  177 |   char charname[MAX_INPUT_LENGTH] = {'\0'}, property[MAX_INPUT_LENGTH] = {'\0'};
      |                                             ^~~~~~~~
dg_misc.c:189:15: warning: the comparison will always evaluate as ‘false’ for the address of ‘value_p’ will never be NULL [-Waddress]
  189 |       value_p == NULL || !*value_p || duration_p == NULL || !*duration_p)
      |               ^~
dg_misc.c:178:8: note: ‘value_p’ declared here
  178 |   char value_p[MAX_INPUT_LENGTH] = {'\0'}, duration_p[MAX_INPUT_LENGTH] = {'\0'};
      |        ^~~~~~~
dg_misc.c:189:50: warning: the comparison will always evaluate as ‘false’ for the address of ‘duration_p’ will never be NULL [-Waddress]
  189 |       value_p == NULL || !*value_p || duration_p == NULL || !*duration_p)
      |                                                  ^~
dg_misc.c:178:44: note: ‘duration_p’ declared here
  178 |   char value_p[MAX_INPUT_LENGTH] = {'\0'}, duration_p[MAX_INPUT_LENGTH] = {'\0'};
      |                                            ^~~~~~~~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o dg_mobcmd.o dg_mobcmd.c
dg_mobcmd.c: In function ‘impl_do_mload_’:
dg_mobcmd.c:461:17: warning: the comparison will always evaluate as ‘true’ for the address of ‘arg1’ will never be NULL [-Waddress]
  461 |     tch = (arg1 != NULL && *arg1 == UID_CHAR) ? get_char(arg1) : get_char_room_vis(ch, arg1, NULL);
      |                 ^~
dg_mobcmd.c:365:8: note: ‘arg1’ declared here
  365 |   char arg1[MAX_INPUT_LENGTH] = {'\0'}, arg2[MAX_INPUT_LENGTH] = {'\0'};
      |        ^~~~
dg_mobcmd.c:464:16: warning: the comparison will always evaluate as ‘true’ for the address of ‘arg2’ will never be NULL [-Waddress]
  464 |       if (arg2 != NULL && *arg2 && (pos = find_eq_pos_script(arg2)) >= 0 &&
      |                ^~
dg_mobcmd.c:365:41: note: ‘arg2’ declared here
  365 |   char arg1[MAX_INPUT_LENGTH] = {'\0'}, arg2[MAX_INPUT_LENGTH] = {'\0'};
      |                                         ^~~~
dg_mobcmd.c:475:17: warning: the comparison will always evaluate as ‘true’ for the address of ‘arg1’ will never be NULL [-Waddress]
  475 |     cnt = (arg1 != NULL && *arg1 == UID_CHAR) ? get_obj(arg1) : get_obj_vis(ch, arg1, NULL);
      |                 ^~
dg_mobcmd.c:365:8: note: ‘arg1’ declared here
  365 |   char arg1[MAX_INPUT_LENGTH] = {'\0'}, arg2[MAX_INPUT_LENGTH] = {'\0'};
      |        ^~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o dg_objcmd.o dg_objcmd.c
dg_objcmd.c: In function ‘do_dgoload’:
dg_objcmd.c:595:16: warning: the comparison will always evaluate as ‘true’ for the address of ‘arg2’ will never be NULL [-Waddress]
  595 |       if (arg2 != NULL && *arg2 && (pos = find_eq_pos_script(arg2)) >= 0 &&
      |                ^~
dg_objcmd.c:505:41: note: ‘arg2’ declared here
  505 |   char arg1[MAX_INPUT_LENGTH] = {'\0'}, arg2[MAX_INPUT_LENGTH] = {'\0'};
      |                                         ^~~~
dg_objcmd.c: In function ‘do_osetval’:
dg_objcmd.c:787:12: warning: the comparison will always evaluate as ‘false’ for the address of ‘arg1’ will never be NULL [-Waddress]
  787 |   if (arg1 == NULL || !*arg1 || arg2 == NULL || !*arg2 || !is_number(arg1) || !is_number(arg2))
      |            ^~
dg_objcmd.c:782:8: note: ‘arg1’ declared here
  782 |   char arg1[MAX_INPUT_LENGTH] = {'\0'}, arg2[MAX_INPUT_LENGTH] = {'\0'};
      |        ^~~~
dg_objcmd.c:787:38: warning: the comparison will always evaluate as ‘false’ for the address of ‘arg2’ will never be NULL [-Waddress]
  787 |   if (arg1 == NULL || !*arg1 || arg2 == NULL || !*arg2 || !is_number(arg1) || !is_number(arg2))
      |                                      ^~
dg_objcmd.c:782:41: note: ‘arg2’ declared here
  782 |   char arg1[MAX_INPUT_LENGTH] = {'\0'}, arg2[MAX_INPUT_LENGTH] = {'\0'};
      |                                         ^~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o dg_olc.o dg_olc.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o dg_scripts.o dg_scripts.c
dg_scripts.c: In function ‘impl_do_detach_’:
dg_scripts.c:1337:16: warning: the comparison will always evaluate as ‘false’ for the address of ‘arg3’ will never be NULL [-Waddress]
 1337 |       if (arg3 == NULL || !*arg3)
      |                ^~
dg_scripts.c:1276:74: note: ‘arg3’ declared here
 1276 |   char arg1[MAX_INPUT_LENGTH] = {'\0'}, arg2[MAX_INPUT_LENGTH] = {'\0'}, arg3[MAX_INPUT_LENGTH] = {'\0'};
      |                                                                          ^~~~
dg_scripts.c:1365:16: warning: the comparison will always evaluate as ‘false’ for the address of ‘arg3’ will never be NULL [-Waddress]
 1365 |       if (arg3 == NULL || !*arg3)
      |                ^~
dg_scripts.c:1276:74: note: ‘arg3’ declared here
 1276 |   char arg1[MAX_INPUT_LENGTH] = {'\0'}, arg2[MAX_INPUT_LENGTH] = {'\0'}, arg3[MAX_INPUT_LENGTH] = {'\0'};
      |                                                                          ^~~~
dg_scripts.c: In function ‘makeuid_var’:
dg_scripts.c:2183:11: warning: the comparison will always evaluate as ‘false’ for the address of ‘arg’ will never be NULL [-Waddress]
 2183 |   if (arg == NULL || !*arg)
      |           ^~
dg_scripts.c:2166:8: note: ‘arg’ declared here
 2166 |   char arg[MAX_INPUT_LENGTH] = {'\0'}, name[MAX_INPUT_LENGTH] = {'\0'};
      |        ^~~
dg_scripts.c:2199:14: warning: the comparison will always evaluate as ‘false’ for the address of ‘name’ will never be NULL [-Waddress]
 2199 |     if (name == NULL || !*name)
      |              ^~
dg_scripts.c:2166:40: note: ‘name’ declared here
 2166 |   char arg[MAX_INPUT_LENGTH] = {'\0'}, name[MAX_INPUT_LENGTH] = {'\0'};
      |                                        ^~~~
dg_scripts.c: In function ‘perform_set_dg_var’:
dg_scripts.c:2512:16: warning: the comparison will always evaluate as ‘false’ for the address of ‘var_name’ will never be NULL [-Waddress]
 2512 |   if (var_name == NULL || !*var_name || var_value == NULL || !*var_value)
      |                ^~
dg_scripts.c:2508:8: note: ‘var_name’ declared here
 2508 |   char var_name[MAX_INPUT_LENGTH] = {'\0'}, *var_value;
      |        ^~~~~~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o dg_triggers.o dg_triggers.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o dg_variables.o dg_variables.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o dg_wldcmd.o dg_wldcmd.c
dg_wldcmd.c: In function ‘do_wload’:
dg_wldcmd.c:556:16: warning: the comparison will always evaluate as ‘true’ for the address of ‘arg2’ will never be NULL [-Waddress]
  556 |       if (arg2 != NULL && *arg2 && (pos = find_eq_pos_script(arg2)) >= 0 &&
      |                ^~
dg_wldcmd.c:476:41: note: ‘arg2’ declared here
  476 |   char arg1[MAX_INPUT_LENGTH] = {'\0'}, arg2[MAX_INPUT_LENGTH] = {'\0'};
      |                                         ^~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o domain_powers.o domain_powers.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o domains_schools.o domains_schools.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o encounters.o encounters.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o evolutions.o evolutions.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o feats.o feats.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o fight.o fight.c
fight.c: In function ‘compute_hit_damage’:
fight.c:7147:39: warning: ‘%d’ directive output may be truncated writing between 1 and 11 bytes into a region of size 10 [-Wformat-truncation=]
 7147 |           snprintf(buf, sizeof(buf), "%d", crippling_critical_var);
      |                                       ^~
fight.c:7147:38: note: directive argument in the range [-2147483647, 2147483647]
 7147 |           snprintf(buf, sizeof(buf), "%d", crippling_critical_var);
      |                                      ^~~~
In file included from /usr/include/stdio.h:980,
                 from sysdep.h:69,
                 from fight.c:14:
In function ‘snprintf’,
    inlined from ‘compute_hit_damage’ at fight.c:7147:11:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 2 and 12 bytes into a destination of size 10
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o gain.o gain.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o genmob.o genmob.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o genobj.o genobj.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o genolc.o genolc.c
genolc.c: In function ‘export_save_rooms’:
genolc.c:1053:11: warning: ‘__builtin_strncpy’ specified bound 49152 equals destination size [-Wstringop-truncation]
 1053 |           strncpy(buf, xdesc->description, sizeof(buf));
      |           ^
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o genqst.o genqst.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o genshp.o genshp.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o genwld.o genwld.c
genwld.c: In function ‘save_rooms’:
genwld.c:450:11: warning: ‘__builtin_strncpy’ specified bound 49152 equals destination size [-Wstringop-truncation]
  450 |           strncpy(buf, xdesc->description, sizeof(buf));
      |           ^
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o genzon.o genzon.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o graph.o graph.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o grapple.o grapple.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o handler.o handler.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o hedit.o hedit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o help.o help.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o helpers.o helpers.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o hlqedit.o hlqedit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o hlquest.o hlquest.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o house.o house.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o hsedit.o hsedit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o hunts.o hunts.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o ibt.o ibt.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o improved-edit.o improved-edit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o interpreter.o interpreter.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o kdtree.o kdtree.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o limits.o limits.c
limits.c: In function ‘recharge_activated_items’:
limits.c:1406:49: warning: ‘%s’ directive output may be truncated writing up to 199 bytes into a region of size 196 [-Wformat-truncation=]
 1406 |                 snprintf(buf, sizeof(buf), "$p, %s, regains 1 charge of '%s'.",
      |                                                 ^~
 1407 |                   where_name, spell_info[obj->activate_spell[ACT_SPELL_SPELLNUM]].name);
      |                   ~~~~~~~~~~                     
In file included from /usr/include/stdio.h:980,
                 from sysdep.h:69,
                 from limits.c:12:
In function ‘snprintf’,
    inlined from ‘recharge_activated_items’ at limits.c:1406:17:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output 30 or more bytes (assuming 229) into a destination of size 200
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o lists.o lists.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o magic.o magic.c
magic.c: In function ‘mag_unaffects’:
magic.c:11368:22: warning: the comparison will always evaluate as ‘true’ for the address of ‘bitvector’ will never be NULL [-Waddress]
11368 |     if (af && affect && af->bitvector && IS_SET_AR(af->bitvector, affect))
      |                      ^~
In file included from magic.c:13:
structs.h:5263:9: note: ‘bitvector’ declared here
 5263 |     int bitvector[AF_ARRAY_MAX]; /**< Tells which bits to set (AFF_XXX). */
      |         ^~~~~~~~~
magic.c:11377:23: warning: the comparison will always evaluate as ‘true’ for the address of ‘bitvector’ will never be NULL [-Waddress]
11377 |     if (af && affect2 && af->bitvector && IS_SET_AR(af->bitvector, affect2))
      |                       ^~
structs.h:5263:9: note: ‘bitvector’ declared here
 5263 |     int bitvector[AF_ARRAY_MAX]; /**< Tells which bits to set (AFF_XXX). */
      |         ^~~~~~~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o mail.o mail.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o medit.o medit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o missions.o missions.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o mobact.o mobact.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o modify.o modify.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o msgedit.o msgedit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o mud_event.o mud_event.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o mysql.o mysql.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o new_mail.o new_mail.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o oasis.o oasis.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o oasis_copy.o oasis_copy.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o oasis_delete.o oasis_delete.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o oasis_list.o oasis_list.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o objsave.o objsave.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o oedit.o oedit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o perfmon_optimized.o perfmon_optimized.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o perlin.o perlin.c
perlin.c:36:31: warning: argument 2 of type ‘double[2]’ with mismatched bound [-Warray-parameter=]
   36 | double noise2(int idx, double vec[2])
      |                        ~~~~~~~^~~~~~
In file included from perlin.c:8:
perlin.h:28:24: note: previously declared as ‘double *’
   28 | double noise2(int idx, double *);
      |                        ^~~~~~~~
perlin.c:78:31: warning: argument 2 of type ‘double[3]’ with mismatched bound [-Warray-parameter=]
   78 | double noise3(int idx, double vec[3])
      |                        ~~~~~~~^~~~~~
perlin.h:29:24: note: previously declared as ‘double *’
   29 | double noise3(int idx, double *);
      |                        ^~~~~~~~
perlin.c:137:24: warning: argument 1 of type ‘double[2]’ with mismatched bound [-Warray-parameter=]
  137 | void normalize2(double v[2])
      |                 ~~~~~~~^~~~
perlin.h:31:17: note: previously declared as ‘double *’
   31 | void normalize2(double *);
      |                 ^~~~~~~~
perlin.c:146:24: warning: argument 1 of type ‘double[3]’ with mismatched bound [-Warray-parameter=]
  146 | void normalize3(double v[3])
      |                 ~~~~~~~^~~~
perlin.h:30:17: note: previously declared as ‘double *’
   30 | void normalize3(double *);
      |                 ^~~~~~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o players.o players.c
players.c: In function ‘save_char’:
players.c:1578:9: warning: the comparison will always evaluate as ‘true’ for the address of ‘host’ will never be NULL [-Waddress]
 1578 |     if (ch->desc->host && *ch->desc->host)
      |         ^~
In file included from players.c:13:
structs.h:5556:10: note: ‘host’ declared here
 5556 |     char host[HOST_LENGTH + 1];        /**< hostname */
      |          ^~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o prefedit.o prefedit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o premadebuilds.o premadebuilds.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o protocol.o protocol.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o psionics.o psionics.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o qedit.o qedit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o quest.o quest.c
quest.c: In function ‘impl_do_quest_’:
quest.c:1722:7: warning: ‘quest_hist’ accessing 49152 bytes in a region of size 512 [-Wstringop-overflow=]
 1722 |       quest_hist(ch, arg2);
      |       ^~~~~~~~~~~~~~~~~~~~
quest.c:1722:7: note: referencing argument 2 of type ‘char[49152]’
quest.c:995:6: note: in a call to function ‘quest_hist’
  995 | void quest_hist(struct char_data *ch, char argument[MAX_STRING_LENGTH])
      |      ^~~~~~~~~~
quest.c:1725:7: warning: ‘quest_quit’ accessing 49152 bytes in a region of size 512 [-Wstringop-overflow=]
 1725 |       quest_quit(ch, arg2);
      |       ^~~~~~~~~~~~~~~~~~~~
quest.c:1725:7: note: referencing argument 2 of type ‘char[49152]’
quest.c:1320:6: note: in a call to function ‘quest_quit’
 1320 | void quest_quit(struct char_data *ch, char argument[MAX_STRING_LENGTH])
      |      ^~~~~~~~~~
quest.c:1728:7: warning: ‘quest_progress’ accessing 49152 bytes in a region of size 512 [-Wstringop-overflow=]
 1728 |       quest_progress(ch, arg2);
      |       ^~~~~~~~~~~~~~~~~~~~~~~~
quest.c:1728:7: note: referencing argument 2 of type ‘char[49152]’
quest.c:1373:6: note: in a call to function ‘quest_progress’
 1373 | void quest_progress(struct char_data *ch, char argument[MAX_STRING_LENGTH])
      |      ^~~~~~~~~~~~~~
quest.c:1734:9: warning: ‘quest_stat’ accessing 49152 bytes in a region of size 512 [-Wstringop-overflow=]
 1734 |         quest_stat(ch, arg2);
      |         ^~~~~~~~~~~~~~~~~~~~
quest.c:1734:9: note: referencing argument 2 of type ‘char[49152]’
quest.c:1526:6: note: in a call to function ‘quest_stat’
 1526 | void quest_stat(struct char_data *ch, char argument[MAX_STRING_LENGTH])
      |      ^~~~~~~~~~
quest.c:1737:7: warning: ‘quest_assign’ accessing 49152 bytes in a region of size 512 [-Wstringop-overflow=]
 1737 |       quest_assign(ch, arg2);
      |       ^~~~~~~~~~~~~~~~~~~~~~
quest.c:1737:7: note: referencing argument 2 of type ‘char[49152]’
quest.c:1480:6: note: in a call to function ‘quest_assign’
 1480 | void quest_assign(struct char_data *ch, char argument[MAX_STRING_LENGTH])
      |      ^~~~~~~~~~~~
In function ‘quest_quit’,
    inlined from ‘impl_do_quest_.isra’ at quest.c:1725:7:
cc1: warning: ‘quest_quit.part.0’ accessing 49152 bytes in a region of size 512 [-Wstringop-overflow=]
cc1: note: referencing argument 2 of type ‘char[49152]’
quest.c: In function ‘impl_do_quest_.isra’:
quest.c:1320:6: note: in a call to function ‘quest_quit.part.0’
 1320 | void quest_quit(struct char_data *ch, char argument[MAX_STRING_LENGTH])
      |      ^~~~~~~~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o race.o race.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o random.o random.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o random_names.o random_names.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o rank.o rank.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o redit.o redit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o roleplay.o roleplay.c
roleplay.c: In function ‘impl_do_showrpinfo_.isra’:
roleplay.c:1595:74: warning: ‘%s’ directive output may be truncated writing up to 99 bytes into a region of size 84 [-Wformat-truncation=]
 1595 |     case SCMD_RP_BG_STORY: snprintf(buf2, sizeof(buf2), " BACKGROUND FOR %s ", buf); text_out = t->player.background; break;
      |                                                                          ^~    ~~~
In file included from /usr/include/stdio.h:980,
                 from sysdep.h:69,
                 from roleplay.c:7:
In function ‘snprintf’,
    inlined from ‘impl_do_showrpinfo_.isra’ at roleplay.c:1595:28:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 18 and 117 bytes into a destination of size 100
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
roleplay.c: In function ‘impl_do_showrpinfo_.isra’:
roleplay.c:1594:66: warning: ‘%s’ directive output may be truncated writing up to 99 bytes into a region of size 89 [-Wformat-truncation=]
 1594 |     case SCMD_RP_FLAWS: snprintf(buf2, sizeof(buf2), " FLAWS FOR %s ", buf); text_out = t->player.flaws; break;
      |                                                                  ^~    ~~~
In function ‘snprintf’,
    inlined from ‘impl_do_showrpinfo_.isra’ at roleplay.c:1594:25:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 13 and 112 bytes into a destination of size 100
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
roleplay.c: In function ‘impl_do_showrpinfo_.isra’:
roleplay.c:1593:66: warning: ‘%s’ directive output may be truncated writing up to 99 bytes into a region of size 89 [-Wformat-truncation=]
 1593 |     case SCMD_RP_BONDS: snprintf(buf2, sizeof(buf2), " BONDS FOR %s ", buf); text_out = t->player.bonds; break;
      |                                                                  ^~    ~~~
In function ‘snprintf’,
    inlined from ‘impl_do_showrpinfo_.isra’ at roleplay.c:1593:25:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 13 and 112 bytes into a destination of size 100
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
roleplay.c: In function ‘impl_do_showrpinfo_.isra’:
roleplay.c:1592:68: warning: ‘%s’ directive output may be truncated writing up to 99 bytes into a region of size 88 [-Wformat-truncation=]
 1592 |     case SCMD_RP_IDEALS: snprintf(buf2, sizeof(buf2), " IDEALS FOR %s ", buf); text_out = t->player.ideals; break;
      |                                                                    ^~    ~~~
In function ‘snprintf’,
    inlined from ‘impl_do_showrpinfo_.isra’ at roleplay.c:1592:26:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 14 and 113 bytes into a destination of size 100
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
roleplay.c: In function ‘impl_do_showrpinfo_.isra’:
roleplay.c:1591:66: warning: ‘%s’ directive output may be truncated writing up to 99 bytes into a region of size 89 [-Wformat-truncation=]
 1591 |     case SCMD_RP_GOALS: snprintf(buf2, sizeof(buf2), " GOALS FOR %s ", buf); text_out = t->player.goals; break;
      |                                                                  ^~    ~~~
In function ‘snprintf’,
    inlined from ‘impl_do_showrpinfo_.isra’ at roleplay.c:1591:25:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 13 and 112 bytes into a destination of size 100
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
roleplay.c: In function ‘impl_do_showrpinfo_.isra’:
roleplay.c:1590:78: warning: ‘%s’ directive output may be truncated writing up to 99 bytes into a region of size 83 [-Wformat-truncation=]
 1590 |     case SCMD_RP_PERSONALITY: snprintf(buf2, sizeof(buf2), " PERSONALITY FOR %s ", buf); text_out = t->player.personality; break;
      |                                                                              ^~    ~~~
In function ‘snprintf’,
    inlined from ‘impl_do_showrpinfo_.isra’ at roleplay.c:1590:31:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 19 and 118 bytes into a destination of size 100
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
roleplay.c: In function ‘impl_do_showrpinfo_.isra’:
roleplay.c:1589:71: warning: ‘%s’ directive output may be truncated writing up to 99 bytes into a region of size 83 [-Wformat-truncation=]
 1589 |     case SCMD_RP_DESC: snprintf(buf2, sizeof(buf2), " DESCRIPTION FOR %s ", buf); text_out = t->player.description; break;
      |                                                                       ^~    ~~~
In function ‘snprintf’,
    inlined from ‘impl_do_showrpinfo_.isra’ at roleplay.c:1589:24:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 19 and 118 bytes into a destination of size 100
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o sedit.o sedit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o shop.o shop.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o spec_abilities.o spec_abilities.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o spec_assign.o spec_assign.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o spec_procs.o spec_procs.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o specs.artifacts.o specs.artifacts.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o spell_parser.o spell_parser.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o spell_prep.o spell_prep.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o spellbook_scroll.o spellbook_scroll.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o spells.o spells.c
spells.c: In function ‘perform_dispel’:
spells.c:553:22: warning: the comparison will always evaluate as ‘true’ for the address of ‘affected_by’ will never be NULL [-Waddress]
  553 |     if (ch->affected || AFF_FLAGS(ch))
      |                      ^~
In file included from spells.c:13:
structs.h:4664:9: note: ‘affected_by’ declared here
 4664 |     int affected_by[AF_ARRAY_MAX]; /**< Bitvector for spells/skills affected by */
      |         ^~~~~~~~~~~
spells.c: In function ‘spell_spellstaff’:
spells.c:2713:32: warning: the comparison will always evaluate as ‘false’ for the address of ‘spellname’ will never be NULL [-Waddress]
 2713 |   if (!*spellname || spellname == NULL)
      |                                ^~
spells.c:2706:8: note: ‘spellname’ declared here
 2706 |   char spellname[MAX_STRING_LENGTH] = {'\0'};
      |        ^~~~~~~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o staff_events.o staff_events.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o study.o study.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o tedit.o tedit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o templates.o templates.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o trade.o trade.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o transport.o transport.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o traps.o traps.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o treasure.o treasure.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o treasure_const.o treasure_const.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o utils.o utils.c
utils.c: In function ‘calculate_max_hp’:
utils.c:7400:54: warning: ‘ = ’ directive output may be truncated writing 3 bytes into a region of size between 1 and 160 [-Wformat-truncation=]
 7400 |           snprintf(line_buf, sizeof(line_buf), "%-40s = %s%d\r\n", temp_buf, aff->modifier > 0 ? "+" : "", aff->modifier);
      |                                                      ^~~
utils.c:7400:48: note: directive argument in the range [-32768, 32767]
 7400 |           snprintf(line_buf, sizeof(line_buf), "%-40s = %s%d\r\n", temp_buf, aff->modifier > 0 ? "+" : "", aff->modifier);
      |                                                ^~~~~~~~~~~~~~~~~~
In file included from /usr/include/stdio.h:980,
                 from sysdep.h:69,
                 from utils.c:14:
In function ‘snprintf’,
    inlined from ‘calculate_max_hp’ at utils.c:7400:11:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 47 and 212 bytes into a destination of size 200
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
utils.c: In function ‘calculate_max_hp’:
utils.c:7412:54: warning: ‘ = ’ directive output may be truncated writing 3 bytes into a region of size between 1 and 160 [-Wformat-truncation=]
 7412 |           snprintf(line_buf, sizeof(line_buf), "%-40s = %d\r\n", temp_buf, aff->modifier);
      |                                                      ^~~
utils.c:7412:48: note: directive argument in the range [-32768, 32767]
 7412 |           snprintf(line_buf, sizeof(line_buf), "%-40s = %d\r\n", temp_buf, aff->modifier);
      |                                                ^~~~~~~~~~~~~~~~
In function ‘snprintf’,
    inlined from ‘calculate_max_hp’ at utils.c:7412:11:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 47 and 211 bytes into a destination of size 200
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
utils.c: In function ‘calculate_max_hp’:
utils.c:7443:56: warning: ‘ = ’ directive output may be truncated writing 3 bytes into a region of size between 1 and 160 [-Wformat-truncation=]
 7443 |             snprintf(line_buf, sizeof(line_buf), "%-40s = %s%d\r\n", temp_buf, obj->affected[j].modifier > 0 ? "+" : "", obj->affected[j].modifier);
      |                                                        ^~~
In function ‘snprintf’,
    inlined from ‘calculate_max_hp’ at utils.c:7443:13:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 47 and 217 bytes into a destination of size 200
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
utils.c: In function ‘calculate_max_hp’:
utils.c:7455:56: warning: ‘ = ’ directive output may be truncated writing 3 bytes into a region of size between 1 and 160 [-Wformat-truncation=]
 7455 |             snprintf(line_buf, sizeof(line_buf), "%-40s = %s%d\r\n", temp_buf, obj->affected[j].modifier > 0 ? "+" : "", obj->affected[j].modifier);
      |                                                        ^~~
In function ‘snprintf’,
    inlined from ‘calculate_max_hp’ at utils.c:7455:13:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 47 and 217 bytes into a destination of size 200
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
utils.c: In function ‘calculate_max_hp’:
utils.c:7493:56: warning: ‘ = ’ directive output may be truncated writing 3 bytes into a region of size between 1 and 160 [-Wformat-truncation=]
 7493 |             snprintf(line_buf, sizeof(line_buf), "%-40s = %s%d\r\n", temp_buf,
      |                                                        ^~~
In function ‘snprintf’,
    inlined from ‘calculate_max_hp’ at utils.c:7493:13:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 47 and 217 bytes into a destination of size 200
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
utils.c: In function ‘calculate_max_hp’:
utils.c:7482:52: warning: ‘ = ’ directive output may be truncated writing 3 bytes into a region of size between 1 and 160 [-Wformat-truncation=]
 7482 |         snprintf(line_buf, sizeof(line_buf), "%-40s = %s%d\r\n", temp_buf, max_value[i] > 0 ? "+" : "", max_value[i]);
      |                                                    ^~~
In function ‘snprintf’,
    inlined from ‘calculate_max_hp’ at utils.c:7482:9:
/usr/include/x86_64-linux-gnu/bits/stdio2.h:54:10: note: ‘__builtin___snprintf_chk’ output between 47 and 217 bytes into a destination of size 200
   54 |   return __builtin___snprintf_chk (__s, __n, __USE_FORTIFY_LEVEL - 1,
      |          ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   55 |                                    __glibc_objsize (__s), __fmt,
      |                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
   56 |                                    __va_arg_pack ());
      |                                    ~~~~~~~~~~~~~~~~~
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o weather.o weather.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o wilderness.o wilderness.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o zedit.o zedit.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o zmalloc.o zmalloc.c
gcc -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********     -c -o zone_procs.o zone_procs.c
g++ -g -O2 -Wall -Wno-char-subscripts -Wno-unused-but-set-variable -Wno-aggressive-loop-optimizations -Wno-unused-value --param=max-vartrack-size=********   -std=c++11   -c -o perfmon.o perfmon.cpp
gcc -o ../bin/circle  account.o act.comm.o act.comm.do_spec_comm.o act.informative.o act.item.o act.movement.o act.offensive.o act.other.o act.social.o act.wizard.o actionqueues.o actions.o aedit.o alchemy.o asciimap.o assign_wpn_armor.o backgrounds.o ban.o bardic_performance.o boards.o bsd-snprintf.o cedit.o char_descs.o clan.o clan_edit.o class.o combat_modes.o comm.o config.o constants.o craft.o crafting_new.o crafting_recipes.o crafts.o db.o deities.o desc_engine.o dg_comm.o dg_db_scripts.o dg_event.o dg_handler.o dg_misc.o dg_mobcmd.o dg_objcmd.o dg_olc.o dg_scripts.o dg_triggers.o dg_variables.o dg_wldcmd.o domain_powers.o domains_schools.o encounters.o evolutions.o feats.o fight.o gain.o genmob.o genobj.o genolc.o genqst.o genshp.o genwld.o genzon.o graph.o grapple.o handler.o hedit.o help.o helpers.o hlqedit.o hlquest.o house.o hsedit.o hunts.o ibt.o improved-edit.o interpreter.o kdtree.o limits.o lists.o magic.o mail.o medit.o missions.o mobact.o modify.o msgedit.o mud_event.o mysql.o new_mail.o oasis.o oasis_copy.o oasis_delete.o oasis_list.o objsave.o oedit.o perfmon_optimized.o perlin.o players.o prefedit.o premadebuilds.o protocol.o psionics.o qedit.o quest.o race.o random.o random_names.o rank.o redit.o roleplay.o sedit.o shop.o spec_abilities.o spec_assign.o spec_procs.o specs.artifacts.o spell_parser.o spell_prep.o spellbook_scroll.o spells.o staff_events.o study.o tedit.o templates.o trade.o transport.o traps.o treasure.o treasure_const.o utils.o weather.o wilderness.o zedit.o zmalloc.o zone_procs.o perfmon.o -lstdc++ -lcrypt -lgd -lm -lmysqlclient
make[1]: Leaving directory '/mnt/c/Projects/Luminari-Source'
make utils
make[1]: Entering directory '/mnt/c/Projects/Luminari-Source'
(cd util; make all)
make[2]: Entering directory '/mnt/c/Projects/Luminari-Source/util'
make[2]: Nothing to be done for 'all'.
make[2]: Leaving directory '/mnt/c/Projects/Luminari-Source/util'
make[1]: Leaving directory '/mnt/c/Projects/Luminari-Source'
