# Attaching Zones to the Wilderness

## 1. Open Trello Resources
- **Builder’s Board:** [https://trello.com/b/xOjCl0hC/luminari-builders](https://trello.com/b/xOjCl0hC/luminari-builders)  
- **Wilderness Map Card:** [https://trello.com/c/5sbBrktg](https://trello.com/c/5sbBrktg)  
  - Open the most recent date folder for the up-to-date map.
  - Download either:
    - `.pdn` (Paint.NET source), or
    - `.png` (web-friendly image)

## 2. Get Pixel Coordinates
- Open the `.png` in a basic image editor (e.g., Paint).
- Bottom-left corner shows pixel location.
- Pick your desired location and **record its pixel coordinates**.

## 3. Convert Coordinates (Optional)
- **Main Zone Document:** [https://trello.com/c/cGNoX1Ea](https://trello.com/c/cGNoX1Ea)  
  - Use the coordinate conversion cells to go from pixel location to wilderness coordinates (or just use wilderness coordinates directly if you already know them).

## 4. In-Game Steps to Attach Your Zone
1. Find the **entrance room** to your zone.
2. Move **one space** in any cardinal direction.
3. Turn **Buildwalk ON**.
4. Move **back** to the original room (this “paints” a single room over the wilderness).
5. Turn **Buildwalk OFF**.
6. Edit that created room:
   - Connect it to your zone.
   - Ensure there is a path back to the wilderness.

## 5. Update Trello After Attaching
- Add your zone to the Wilderness Map (images on Trello):  
  [https://trello.com/c/5sbBrktg](https://trello.com/c/5sbBrktg)
- Add your zone to the **Main Zone Document**:  
  [https://trello.com/c/cGNoX1Ea](https://trello.com/c/cGNoX1Ea)

## 6. Announce In-Game
Type in-game:
```

Change <insert your message about your zone here>

```
